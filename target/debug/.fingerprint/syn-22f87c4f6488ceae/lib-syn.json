{"rustc": 17575471286409424799, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 3033921117576893, "path": 15889559725867233123, "deps": [[373107762698212489, "proc_macro2", false, 2036028256901791004], [1988483478007900009, "unicode_ident", false, 5118608432676288261], [17990358020177143287, "quote", false, 7730136714539470579]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-22f87c4f6488ceae/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}