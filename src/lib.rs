use std::collections::HashMap;
use std::sync::{<PERSON>, Mutex};
use std::time::{Duration, Instant};
use tokio::sync::mpsc;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, <PERSON>ialEq)]
pub enum NodeState {
    <PERSON><PERSON>,
    <PERSON>di<PERSON>,
    Leader,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct LogEntry {
    pub term: u64,
    pub index: u64,
    pub command: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct VoteRequest {
    pub term: u64,
    pub candidate_id: u64,
    pub last_log_index: u64,
    pub last_log_term: u64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct VoteResponse {
    pub term: u64,
    pub vote_granted: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AppendEntriesRequest {
    pub term: u64,
    pub leader_id: u64,
    pub prev_log_index: u64,
    pub prev_log_term: u64,
    pub entries: Vec<LogEntry>,
    pub leader_commit: u64,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct AppendEntriesResponse {
    pub term: u64,
    pub success: bool,
}

pub struct RaftNode {
    pub id: u64,
    pub state: NodeState,
    pub current_term: u64,
    pub voted_for: Option<u64>,
    pub log: Vec<LogEntry>,
    pub commit_index: u64,
    pub last_applied: u64,
    pub peers: Vec<u64>,
    pub last_heartbeat: Instant,
    pub election_timeout: Duration,
}

impl RaftNode {
    pub fn new(id: u64, peers: Vec<u64>) -> Self {
        Self {
            id,
            state: NodeState::Follower,
            current_term: 0,
            voted_for: None,
            log: vec![],
            commit_index: 0,
            last_applied: 0,
            peers,
            last_heartbeat: Instant::now(),
            election_timeout: Duration::from_millis(150 + (id * 50)), // 随机化选举超时
        }
    }

    pub fn start_election(&mut self) -> VoteRequest {
        self.state = NodeState::Candidate;
        self.current_term += 1;
        self.voted_for = Some(self.id);
        self.last_heartbeat = Instant::now();

        let last_log_index = self.log.len() as u64;
        let last_log_term = self.log.last().map(|e| e.term).unwrap_or(0);

        VoteRequest {
            term: self.current_term,
            candidate_id: self.id,
            last_log_index,
            last_log_term,
        }
    }

    pub fn handle_vote_request(&mut self, req: VoteRequest) -> VoteResponse {
        let mut vote_granted = false;

        if req.term > self.current_term {
            self.current_term = req.term;
            self.voted_for = None;
            self.state = NodeState::Follower;
        }

        if req.term == self.current_term {
            let can_vote = self.voted_for.is_none() || self.voted_for == Some(req.candidate_id);
            let log_ok = self.is_log_up_to_date(req.last_log_index, req.last_log_term);
            
            if can_vote && log_ok {
                vote_granted = true;
                self.voted_for = Some(req.candidate_id);
                self.last_heartbeat = Instant::now();
            }
        }

        VoteResponse {
            term: self.current_term,
            vote_granted,
        }
    }

    pub fn handle_append_entries(&mut self, req: AppendEntriesRequest) -> AppendEntriesResponse {
        let mut success = false;

        if req.term >= self.current_term {
            self.current_term = req.term;
            self.state = NodeState::Follower;
            self.last_heartbeat = Instant::now();

            // 检查日志一致性
            if req.prev_log_index == 0 || 
               (req.prev_log_index <= self.log.len() as u64 &&
                self.log.get((req.prev_log_index - 1) as usize)
                    .map(|e| e.term) == Some(req.prev_log_term)) {
                
                // 追加新条目
                if !req.entries.is_empty() {
                    self.log.truncate(req.prev_log_index as usize);
                    self.log.extend(req.entries);
                }

                // 更新提交索引
                if req.leader_commit > self.commit_index {
                    self.commit_index = std::cmp::min(req.leader_commit, self.log.len() as u64);
                }

                success = true;
            }
        }

        AppendEntriesResponse {
            term: self.current_term,
            success,
        }
    }

    fn is_log_up_to_date(&self, last_log_index: u64, last_log_term: u64) -> bool {
        let our_last_term = self.log.last().map(|e| e.term).unwrap_or(0);
        let our_last_index = self.log.len() as u64;

        last_log_term > our_last_term || 
        (last_log_term == our_last_term && last_log_index >= our_last_index)
    }

    pub fn should_start_election(&self) -> bool {
        matches!(self.state, NodeState::Follower | NodeState::Candidate) &&
        self.last_heartbeat.elapsed() > self.election_timeout
    }

    pub fn become_leader(&mut self) {
        self.state = NodeState::Leader;
        println!("Node {} became leader for term {}", self.id, self.current_term);
    }
}