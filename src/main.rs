use tokio::time::{sleep, Duration};
use std::sync::{Arc, Mutex};
use std::collections::HashMap;

mod lib;
use lib::*;

#[tokio::main]
async fn main() {
    println!("启动 Raft 分布式一致性协议 Demo");

    // 创建3个节点的集群
    let nodes = Arc::new(Mutex::new(HashMap::new()));
    
    for i in 1..=3 {
        let peers = vec![1, 2, 3].into_iter().filter(|&x| x != i).collect();
        let node = RaftNode::new(i, peers);
        nodes.lock().unwrap().insert(i, node);
    }

    // 启动节点运行循环
    let nodes_clone = nodes.clone();
    tokio::spawn(async move {
        loop {
            sleep(Duration::from_millis(50)).await;
            
            let mut nodes_guard = nodes_clone.lock().unwrap();
            let node_ids: Vec<u64> = nodes_guard.keys().cloned().collect();
            
            for node_id in node_ids {
                if let Some(node) = nodes_guard.get_mut(&node_id) {
                    if node.should_start_election() {
                        let vote_req = node.start_election();
                        println!("Node {} 开始选举，term: {}", node_id, vote_req.term);
                        
                        // 模拟投票过程
                        let mut votes = 1; // 自己投自己
                        for &peer_id in &node.peers.clone() {
                            if let Some(peer) = nodes_guard.get_mut(&peer_id) {
                                let response = peer.handle_vote_request(vote_req.clone());
                                if response.vote_granted {
                                    votes += 1;
                                }
                            }
                        }
                        
                        // 检查是否获得多数票
                        if votes > (nodes_guard.len() / 2) {
                            if let Some(leader) = nodes_guard.get_mut(&node_id) {
                                leader.become_leader();
                            }
                        }
                    }
                }
            }
        }
    });

    // 模拟客户端请求
    tokio::spawn(async move {
        sleep(Duration::from_secs(2)).await;
        
        loop {
            sleep(Duration::from_secs(3)).await;
            
            let nodes_guard = nodes.lock().unwrap();
            if let Some((leader_id, _)) = nodes_guard.iter()
                .find(|(_, node)| matches!(node.state, NodeState::Leader)) {
                println!("向 Leader {} 发送数据写入请求", leader_id);
            }
        }
    });

    // 保持程序运行
    loop {
        sleep(Duration::from_secs(1)).await;
    }
}